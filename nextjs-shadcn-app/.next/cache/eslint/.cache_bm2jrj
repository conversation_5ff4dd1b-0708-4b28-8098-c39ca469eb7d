[{"/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/app/layout.tsx": "1", "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/app/page.tsx": "2", "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/button.tsx": "3", "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/card.tsx": "4", "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/input.tsx": "5", "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/label.tsx": "6", "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/lib/utils.ts": "7"}, {"size": 689, "mtime": 1751646741081, "results": "8", "hashOfConfig": "9"}, {"size": 6335, "mtime": 1751647145448, "results": "10", "hashOfConfig": "9"}, {"size": 2123, "mtime": 1751646878548, "results": "11", "hashOfConfig": "9"}, {"size": 1989, "mtime": 1751646878566, "results": "12", "hashOfConfig": "9"}, {"size": 967, "mtime": 1751646878569, "results": "13", "hashOfConfig": "9"}, {"size": 611, "mtime": 1751646878572, "results": "14", "hashOfConfig": "9"}, {"size": 166, "mtime": 1751646837662, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bh4w7x", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/button.tsx", [], [], "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/card.tsx", [], [], "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/input.tsx", [], [], "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/components/ui/label.tsx", [], [], "/Users/<USER>/Desktop/ui-design(ML)/nextjs-shadcn-app/src/lib/utils.ts", [], []]