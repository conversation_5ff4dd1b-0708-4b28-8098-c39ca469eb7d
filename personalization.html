<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Personalize your ቀለሜ account</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    rel="stylesheet"
  />
  <style>
    .glass-backdrop {
      backdrop-filter: blur(20px);
      background: rgba(255,255,255,0.1);
      border: 1px solid rgba(255,255,255,0.2);
    }
    .glass-input {
      backdrop-filter: blur(10px);
      background: rgba(255,255,255,0.1);
      border: 1px solid rgba(255,255,255,0.2);
    }
    .glass-input:focus {
      background: rgba(255,255,255,0.15);
      border-color: rgba(255,255,255,0.3);
    }
    .step-inactive {
      background: rgba(255,255,255,0.1);
      border: 1px solid rgba(255,255,255,0.25);
      color: rgba(255,255,255,0.75);
    }
    .step-active {
      background: rgba(255,255,255,0.35);
      border: 1px solid rgba(255,255,255,0.6);
      color: #fff;
    }
    .fade-in {
      animation: fadeIn .8s ease-out forwards;
      opacity: 0;
      transform: translateY(20px);
    }
    .fade-in-delay-1 { animation-delay: .1s; }
    .fade-in-delay-2 { animation-delay: .2s; }
    .fade-in-delay-3 { animation-delay: .3s; }
    .fade-in-delay-4 { animation-delay: .4s; }
    .fade-in-delay-5 { animation-delay: .5s; }
    .fade-in-delay-6 { animation-delay: .6s; }
    @keyframes fadeIn {
      to { opacity: 1; transform: translateY(0); }
    }
    select option {
      background: rgba(0,0,0,0.8);
      color: #fff;
    }
  </style>
</head>
<body
  class="relative min-h-screen flex items-center justify-center p-4 font-inter"
  style="
    background-image:
      linear-gradient(rgba(0,0,0,0.7),rgba(0,0,0,0.7)),
      url('https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=2160&q=80');
    background-size: cover;
    background-position: center;
  "
>
  <!-- Glass container -->
  <div
    class="relative z-10 w-full max-w-md glass-backdrop fade-in text-white rounded-2xl p-8"
  >
    <!-- Logo & Heading -->
    <div class="text-center mb-8 fade-in-delay-1">
      <div
        class="w-16 h-16 mx-auto mb-4 glass-backdrop rounded-xl flex items-center justify-center"
      >
        <!-- replace inner div with your logo IMG if you like -->
        <div class="w-8 h-8 bg-gradient-to-br from-white/60 to-white/30 rounded-lg"></div>
      </div>
      <h1 class="text-3xl font-light tracking-tight mb-2">
        Personalize your account
      </h1>
      <p class="text-sm text-white/70">
        Tell us a bit more so we can tailor your experience.
      </p>
    </div>

    <!-- Progress Bar -->
    <div class="flex items-center justify-center fade-in-delay-2 mb-8">
      <!-- Step 1 -->
      <div class="flex flex-col items-center">
        <div class="w-9 h-9 flex items-center justify-center rounded-full step-inactive text-sm font-medium">
          1
        </div>
        <span class="text-xs mt-1">Sign&nbsp;Up</span>
      </div>
      <div class="flex-1 h-px bg-white/30 mx-2"></div>
      <!-- Step 2 -->
      <div class="flex flex-col items-center">
        <div class="w-9 h-9 flex items-center justify-center rounded-full step-inactive text-sm font-medium">
          2
        </div>
        <span class="text-xs mt-1">Verification</span>
      </div>
      <div class="flex-1 h-px bg-white/30 mx-2"></div>
      <!-- Step 3 -->
      <div class="flex flex-col items-center">
        <div class="w-9 h-9 flex items-center justify-center rounded-full step-active text-sm font-medium">
          3
        </div>
        <span class="text-xs mt-1">Personalization</span>
      </div>
    </div>

    <!-- Form -->
    <div class="space-y-6 fade-in-delay-3">
      <div>
        <h2 class="text-xl font-medium mb-1">Personal details</h2>
        <p class="text-sm text-white/60">Step 3 of 3</p>
      </div>

      <form action="#" method="post" class="space-y-4">
        <!-- Username -->
        <div class="fade-in-delay-4">
          <label for="username" class="block text-sm font-medium mb-2">
            Username
          </label>
          <input
            type="text"
            id="username"
            placeholder="adel_abdu"
            required
            class="w-full px-4 py-3 rounded-xl glass-input placeholder-white/50 focus:outline-none transition-all duration-200 text-white"
          />
        </div>

        <!-- Grade -->
        <div class="fade-in-delay-5">
          <label for="grade" class="block text-sm font-medium mb-2">
            Grade
          </label>
          <select
            id="grade"
            required
            class="w-full px-4 py-3 rounded-xl glass-input text-white focus:outline-none transition-all duration-200 appearance-none cursor-pointer"
          >
            <option value="" disabled selected class="text-white/50">
              Select your grade
            </option>
            <option value="6">Grade 6</option>
            <option value="7">Grade 7</option>
            <option value="8">Grade 8</option>
            <option value="9">Grade 9</option>
            <option value="10">Grade 10</option>
            <option value="11">Grade 11</option>
            <option value="12">Grade 12</option>
            <option value="university">University</option>
          </select>
        </div>

        <!-- Submit -->
        <button
          type="submit"
          class="w-full py-4 rounded-xl glass-backdrop font-medium text-white flex items-center justify-center gap-2 hover:bg-white/20 transition-all duration-200 fade-in-delay-6"
        >
          Finish &amp; Start Exploring
          <svg
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path d="M5 12h14m-7-7l7 7-7 7"></path>
          </svg>
        </button>
      </form>
    </div>
  </div>
</body>
</html>