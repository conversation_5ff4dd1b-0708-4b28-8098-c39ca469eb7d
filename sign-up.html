<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Create your ቀለሜ account – Sign up</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .glass-filter{filter:url(#glass-distortion);}
    .backdrop-blur-sm{backdrop-filter:blur(3px);}
    .transition-custom{transition:all .4s cubic-bezier(.175,.885,.32,2.2);}
    .beautiful-shadow{box-shadow:
      0 2.8px 2.2px rgba(0,0,0,.034),
      0 6.7px 5.3px rgba(0,0,0,.048),
      0 12.5px 10px rgba(0,0,0,.06),
      0 22.3px 17.9px rgba(0,0,0,.072),
      0 41.8px 33.4px rgba(0,0,0,.086),
      0 100px 80px rgba(0,0,0,.12);
    }
    .step-indicator{transition:all .3s ease;}
    .step-indicator.active{background:linear-gradient(135deg,rgba(255,255,255,.4),rgba(255,255,255,.2));}
  </style>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="relative flex items-center justify-center min-h-screen font-light bg-[url(https://images.unsplash.com/photo-*************-429f420c6b9d?w=2160&q=80)] bg-cover">
  <!-- Dark overlay -->
  <div class="absolute inset-0 bg-black/60"></div>

  <div class="relative z-10 flex flex-col overflow-hidden cursor-default beautiful-shadow transition-custom max-w-2xl w-full font-semibold text-white rounded-3xl mx-4">
    <!-- glass layers -->
    <div class="absolute inset-0 z-0 backdrop-blur-md glass-filter isolate"></div>
    <div class="absolute inset-0 z-10 bg-white bg-opacity-15"></div>
    <div class="absolute inset-0 z-20 overflow-hidden shadow-inner"
         style="box-shadow:inset 2px 2px 1px 0 rgba(255,255,255,.5),inset -1px -1px 1px 1px rgba(255,255,255,.5);border-radius:24px;">
    </div>

    <!-- Header -->
    <div class="z-30 flex flex-col items-center justify-center text-center bg-black/10 pt-8 px-8">
      <div class="mb-4">
        <div class="relative inline-flex items-center justify-center w-24 h-24 rounded-2xl mb-3 overflow-hidden">
          <div class="absolute inset-0 z-0 backdrop-blur-sm glass-filter"></div>
          <div class="absolute inset-0 z-10 bg-gradient-to-br from-white/30 to-white/10"></div>
          <div class="absolute inset-0 z-20"
               style="box-shadow:inset 3px 3px 2px 0 rgba(255,255,255,.6),inset -2px -2px 2px 2px rgba(255,255,255,.4);border-radius:16px;">
          </div>
          <img src="logo.png" alt="ቀለሜ Logo" class="z-20 w-full h-full object-contain">
        </div>
        <h1 class="text-5xl font-normal tracking-tighter mb-2">Create your ቀለሜ account</h1>
        <p class="text-sm font-light text-white/80">Let’s get you set up with your new account in just a few simple steps.</p>
      </div>

      <!-- Progress -->
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-2">
          <div class="step-indicator active w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm">1</div>
          <span class="text-xs font-medium text-white/90 hidden sm:block">Sign up</span>
        </div>
        <div class="w-6 h-px bg-white/30"></div>
        <div class="flex items-center gap-2">
          <div class="step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm bg-white/10">2</div>
          <span class="text-xs font-medium text-white/60 hidden sm:block">Verification</span>
        </div>
        <div class="w-6 h-px bg-white/30"></div>
        <div class="flex items-center gap-2">
          <div class="step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm bg-white/10">3</div>
          <span class="text-xs font-medium text-white/60 hidden sm:block">Personalization</span>
        </div>
      </div>
    </div>

    <!-- Form -->
    <div class="z-30 flex flex-col p-8 overflow-y-auto">
      <div class="mb-6">
        <h2 class="text-2xl font-medium mb-2">Tell us about yourself</h2>
        <p class="text-sm font-normal text-white/70">Step 1 of 3 • This helps us personalize your experience</p>
      </div>

      <form action="verification.html" method="get" class="flex-1 mb-6 space-y-4">
        <!-- First & Last Name -->
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label for="firstName" class="block text-sm font-medium mb-2">First name</label>
            <div class="relative overflow-hidden rounded-xl">
              <div class="absolute inset-0 z-0 backdrop-blur-sm glass-filter"></div>
              <div class="absolute inset-0 z-10 bg-white bg-opacity-10"></div>
              <div class="absolute inset-0 z-20"
                   style="box-shadow:inset 1px 1px 1px 0 rgba(255,255,255,.3),inset -1px -1px 1px 1px rgba(255,255,255,.1);border-radius:12px;">
              </div>
              <input id="firstName" type="text" placeholder="adel" required
                     class="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none">
            </div>
          </div>
          <div>
            <label for="lastName" class="block text-sm font-medium mb-2">Last name</label>
            <div class="relative overflow-hidden rounded-xl">
              <div class="absolute inset-0 z-0 backdrop-blur-sm glass-filter"></div>
              <div class="absolute inset-0 z-10 bg-white bg-opacity-10"></div>
              <div class="absolute inset-0 z-20"
                   style="box-shadow:inset 1px 1px 1px 0 rgba(255,255,255,.3),inset -1px -1px 1px 1px rgba(255,255,255,.1);border-radius:12px;">
              </div>
              <input id="lastName" type="text" placeholder="abdu" required
                     class="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none">
            </div>
          </div>
        </div>

        <!-- Email or Phone -->
        <div>
          <label for="contact" class="block text-sm font-medium mb-2">Email or phone number</label>
          <div class="relative overflow-hidden rounded-xl">
            <div class="absolute inset-0 z-0 backdrop-blur-sm glass-filter"></div>
            <div class="absolute inset-0 z-10 bg-white bg-opacity-10"></div>
            <div class="absolute inset-0 z-20"
                 style="box-shadow:inset 1px 1px 1px 0 rgba(255,255,255,.3),inset -1px -1px 1px 1px rgba(255,255,255,.1);border-radius:12px;">
            </div>
            <!-- icon -->
            <svg class="pointer-events-none absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-white/60"
                 fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                 viewBox="0 0 24 24">
              <path d="M22 12h-4m-4 0H2m0 0c0 2.21 3.582 4 8 4s8-1.79 8-4m-16 0c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
            </svg>
            <input id="contact" type="text" inputmode="email" autocomplete="email tel"
                   placeholder="<EMAIL> or +251 9 123 45678" required
                   class="z-30 relative w-full bg-transparent pl-10 pr-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none">
          </div>
        </div>

        <!-- Password -->
        <div>
          <label for="password" class="block text-sm font-medium mb-2">Create password</label>
          <div class="relative overflow-hidden rounded-xl">
            <div class="absolute inset-0 z-0 backdrop-blur-sm glass-filter"></div>
            <div class="absolute inset-0 z-10 bg-white bg-opacity-10"></div>
            <div class="absolute inset-0 z-20"
                 style="box-shadow:inset 1px 1px 1px 0 rgba(255,255,255,.3),inset -1px -1px 1px 1px rgba(255,255,255,.1);border-radius:12px;">
            </div>
            <input id="password" type="password" placeholder="Minimum 8 characters" required
                   class="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none">
          </div>
        </div>

        <!-- Terms -->
        <div class="flex gap-3 pt-2 items-center">
          <input type="checkbox" required
                 class="w-4 h-4 bg-opacity-20 border-opacity-30 focus:ring-0 focus:ring-offset-0 bg-white border-white rounded">
          <p class="text-sm font-normal leading-relaxed text-white/70">
            I agree to the
            <a href="#" class="underline font-medium hover:opacity-80">Terms of Service</a>
            and
            <a href="#" class="underline font-medium hover:opacity-80">Privacy Policy</a>.
          </p>
        </div>

        <!-- Continue -->
        <div class="relative overflow-hidden rounded-xl transition-custom hover:shadow-lg">
          <div class="absolute inset-0 z-0 backdrop-blur-sm glass-filter"></div>
          <div class="absolute inset-0 z-10 bg-gradient-to-r from-white/30 to-white/20"></div>
          <div class="absolute inset-0 z-20"
               style="box-shadow:inset 2px 2px 1px 0 rgba(255,255,255,.5),inset -1px -1px 1px 1px rgba(255,255,255,.3);border-radius:12px;">
          </div>
          <button type="submit"
                  class="z-30 relative w-full py-4 flex items-center justify-center gap-2 text-base font-semibold text-white bg-transparent">
            Continue to Step 2
            <svg width="16" height="16" fill="none" stroke="currentColor" stroke-linecap="round"
                 stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24">
              <path d="M13 7l5 5-5 5M6 12h12"/>
            </svg>
          </button>
        </div>
      </form>

      <!-- Footer -->
      <div class="text-center mt-auto">
        <p class="text-sm font-normal text-white/70 mb-3">
          Already have an account?
          <a href="#" class="font-semibold hover:opacity-80">Sign in here</a>
        </p>
        <div class="flex items-center justify-center gap-4 text-xs font-normal text-white/50">
          <a href="#" class="hover:text-white/70">Help Center</a>
          <span>•</span>
          <a href="#" class="hover:text-white/70">Contact Support</a>
        </div>
      </div>
    </div>
  </div>
</body>
</html>