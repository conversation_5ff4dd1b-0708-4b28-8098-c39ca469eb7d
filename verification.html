<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <title>Verify your ቀለሜ account</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    rel="stylesheet"
  />
  <style>
    .glass-filter { filter: url(#glass-distortion); }
    .beautiful-shadow {
      box-shadow:
        0 2.8px 2.2px rgba(0,0,0,0.034),
        0 6.7px 5.3px rgba(0,0,0,0.048),
        0 12.5px 10px rgba(0,0,0,0.06),
        0 22.3px 17.9px rgba(0,0,0,0.072),
        0 41.8px 33.4px rgba(0,0,0,0.086),
        0 100px 80px rgba(0,0,0,0.12);
    }
    .step-indicator { transition: all .3s ease; }
    .step-indicator.active {
      background: linear-gradient(135deg, rgba(255,255,255,0.4), rgba(255,255,255,0.2));
    }
  </style>
</head>
<body class="flex items-center justify-center min-h-screen bg-[url(https://images.unsplash.com/photo-*************-429f420c6b9d?w=2160&q=80)] bg-cover bg-[rgba(0,0,0,0.75)] bg-blend-multiply font-inter">

  <div class="relative flex flex-col overflow-hidden rounded-3xl max-w-2xl w-full mx-4 text-white font-medium beautiful-shadow transition-all">
    <!-- glass layers -->
    <div class="absolute inset-0 backdrop-blur-md glass-filter"></div>
    <div class="absolute inset-0 bg-white/15"></div>
    <div
      class="absolute inset-0 shadow-inner"
      style="box-shadow: inset 2px 2px 1px 0 rgba(255,255,255,0.5), inset -1px -1px 1px 1px rgba(255,255,255,0.5); border-radius:24px;"
    ></div>

    <!-- Header -->
    <div class="z-20 pt-8 px-8 text-center">
      <div class="relative inline-flex items-center justify-center w-24 h-24 rounded-2xl mx-auto mb-4 overflow-hidden">
        <div class="absolute inset-0 backdrop-blur-sm glass-filter"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-white/30 to-white/10"></div>
        <div
          class="absolute inset-0"
          style="box-shadow: inset 3px 3px 2px 0 rgba(255,255,255,0.6), inset -2px -2px 2px 2px rgba(255,255,255,0.4); border-radius:16px;"
        ></div>
        <img src="logo.png" alt="ቀለሜ Logo" class="z-20 w-full h-full object-contain" />
      </div>
      <h1 class="text-5xl tracking-tight font-normal mb-2">Verify your account</h1>
      <p class="text-white/80 text-sm font-light">Enter the 6-digit code we just sent you.</p>

      <!-- Progress -->
      <div class="flex items-center justify-center gap-3 mt-6">
        <!-- Step 1 -->
        <div class="flex items-center gap-2">
          <div class="step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm bg-white/10">
            1
          </div>
          <span class="hidden sm:block text-xs text-white/60">Sign up</span>
        </div>
        <div class="w-6 h-px bg-white/30"></div>
        <!-- Step 2 -->
        <div class="flex items-center gap-2">
          <div class="step-indicator active w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm">
            2
          </div>
          <span class="hidden sm:block text-xs text-white/90">Verification</span>
        </div>
        <div class="w-6 h-px bg-white/30"></div>
        <!-- Step 3 -->
        <div class="flex items-center gap-2">
          <div class="step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm bg-white/10">
            3
          </div>
          <span class="hidden sm:block text-xs text-white/60">Personalization</span>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="z-20 p-8 space-y-6 overflow-y-auto">
      <h2 class="text-2xl font-medium">Enter your one-time code</h2>
      <p class="text-sm text-white/70 font-normal">Step 2 of 3</p>

      <form action="personalization.html" method="get" class="space-y-6">
        <!-- OTP Input -->
        <div>
          <label for="otp" class="block text-sm mb-2">6-digit code</label>
          <input
            id="otp"
            name="otp"
            type="text"
            maxlength="6"
            required
            placeholder="123456"
            autocomplete="one-time-code"
            aria-label="One time code"
            class="w-full rounded-xl bg-white/25 border border-white/50 pt-3 pb-3 pl-4 pr-4 text-center tracking-widest placeholder-gray-300 text-white backdrop-blur-sm glass-filter hover:bg-white/30 focus:bg-white/35 focus:outline-none focus:ring-2 focus:ring-white/60 transition-colors"
          />
        </div>

        <p class="text-xs text-white/70">
          Didn’t receive it?
          <button type="button" class="underline">Resend code</button>
        </p>

        <!-- Button Group -->
        <div class="flex gap-4">
          <!-- Back -->
          <button
            type="button"
            onclick="history.back()"
            class="relative flex-1 py-4 rounded-xl border border-white/40 backdrop-blur-sm glass-filter text-base font-semibold text-white hover:border-white/70 hover:bg-white/10 transition-colors flex items-center justify-center gap-2"
          >
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"/>
            </svg>
            Back
          </button>

          <!-- Continue -->
          <button
            type="submit"
            class="relative flex-1 py-4 rounded-xl bg-gradient-to-r from-white/30 to-white/20 backdrop-blur-sm glass-filter text-base font-semibold text-white hover:from-white/50 hover:to-white/30 transition-colors flex items-center justify-center gap-2"
          >
            Step 3
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
            </svg>
          </button>
        </div>
      </form>
    </div>
  </div>
</body>
</html>